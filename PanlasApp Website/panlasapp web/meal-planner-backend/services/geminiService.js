const axios = require('axios');

class GeminiService {
  constructor() {
    this.apiKey = process.env.GEMINI_API_KEY || 'AIzaSyApLfuTDjZfegcpfzKqL37OpmL3WB3cALA';
    this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';
    this.model = 'gemini-2.0-flash';

    // Available dietary restrictions in the app (must match frontend options exactly)
    this.AVAILABLE_DIETARY_RESTRICTIONS = [
      'Vegetarian',
      'Vegan',
      'Dairy-Free',
      'Egg-Free',
      'Gluten-Free',
      'Soy-Free',
      'Nut-Free',
      'Low-Carb',
      'Low-Sugar',
      'Sugar-Free',
      'Low-Fat',
      'Low-Sodium',
      'Organic',
      'Halal',
      'High-Protein',
      'Pescatarian',
      'Keto',
      'Plant-Based',
      'Kosher',
      'Climatarian',
      'Raw Food',
      'Mediterranean',
      'Paleo',
      'Kangatarian',
      'Pollotarian',
      'Flexitarian'
    ];

    // Available allergy options in the app (must match frontend options exactly)
    this.AVAILABLE_ALLERGIES = [
      'Milk',
      'Eggs',
      'Fish',
      'Shellfish',
      'Peanuts',
      'Tree Nuts',
      'Wheat',
      'Soybeans',
      'Sesame',
      'Mustard',
      'Celery',
      'Lupin',
      'Mollusks',
      'Sulfites'
    ];
  }

  async generateContent(prompt) {
    try {
      const response = await axios.post(
        `${this.baseUrl}?key=${this.apiKey}`,
        {
          contents: [
            {
              parts: [
                {
                  text: prompt
                }
              ]
            }
          ],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024,
          }
        },
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
      return response.data.candidates[0].content.parts[0].text;
    } catch (error) {
      console.error('Gemini API Error:', error.response?.data || error.message);
      throw new Error('Failed to generate AI response');
    }
  }

  // Detect conflicts in dietary preferences
  async detectDietaryConflicts(data) {
    // Handle both old format (direct preferences) and new format (with user and family)
    let userPreferences, familyMembers;
    if (data.userPreferences) {
      // New format with user and family preferences
      userPreferences = data.userPreferences;
      familyMembers = data.familyMembers || [];
    } else {
      // Old format - direct preferences (for backward compatibility)
      userPreferences = {
        restrictions: data.restrictions || [],
        allergies: data.allergies || [],
        dislikedIngredients: data.dislikedIngredients || []
      };
      familyMembers = [];
    }

    // Dietary definitions for Gemini (improves reasoning for new/rare options)
    const definitions = `
DEFINITIONS:
- Vegetarian: No meat, poultry, or fish.
- Vegan: No animal products at all.
- Dairy-Free: No milk or milk products.
- Egg-Free: No eggs or egg products.
- Gluten-Free: No wheat, barley, rye, or gluten-containing grains.
- Soy-Free: No soy or soy products.
- Nut-Free: No peanuts or tree nuts.
- Low-Carb: Limited carbohydrate intake.
- Low-Sugar: Limited sugar intake.
- Sugar-Free: No sugar.
- Low-Fat: Limited fat intake.
- Low-Sodium: Limited salt intake.
- Organic: Only organic foods.
- Halal: Permitted by Islamic law.
- High-Protein: High in protein.
- Pescatarian: No meat except fish/seafood.
- Keto: Very low carb, high fat.
- Plant-Based: Mostly or only plants.
- Kosher: Permitted by Jewish law.
- Climatarian: Diet chosen for low environmental impact.
- Raw Food: Only raw, uncooked foods.
- Mediterranean: Based on Mediterranean region foods.
- Paleo: Foods presumed eaten by early humans.
- Kangatarian: Vegetarian but eats kangaroo meat.
- Pollotarian: Vegetarian but eats poultry.
- Flexitarian: Mostly vegetarian but occasionally eats meat or fish.

ALLERGY DEFINITIONS:
- Milk: Allergy to cow's milk and dairy products.
- Eggs: Allergy to eggs and egg products.
- Fish: Allergy to finned fish.
- Shellfish: Allergy to crustaceans and mollusks.
- Peanuts: Allergy to peanuts (legumes).
- Tree Nuts: Allergy to nuts from trees (e.g., almonds, walnuts).
- Wheat: Allergy to wheat and wheat products.
- Soybeans: Allergy to soy and soy products.
- Sesame: Allergy to sesame seeds and products.
- Mustard: Allergy to mustard seeds and products.
- Celery: Allergy to celery and celery products.
- Lupin: Allergy to lupin beans and products.
- Mollusks: Allergy to mollusks (e.g., clams, oysters).
- Sufites: Sensitivity to sulfite preservatives.
`;

    let prompt = `
${definitions}
Analyze the following dietary preferences for conflicts and contradictions:
USER PREFERENCES:
Dietary Restrictions: ${userPreferences.restrictions?.join(', ') || 'None'}
Allergies: ${userPreferences.allergies?.join(', ') || 'None'}
Disliked Ingredients: ${userPreferences.dislikedIngredients?.join(', ') || 'None'}
`;

    if (familyMembers.length > 0) {
      prompt += `
FAMILY MEMBERS PREFERENCES:
`;
      familyMembers.forEach((member) => {
        prompt += `
${member.name}:
- Dietary Restrictions: ${member.restrictions?.join(', ') || 'None'}
- Allergies: ${member.allergies?.join(', ') || 'None'}
- Disliked Ingredients: ${member.dislikedIngredients?.join(', ') || 'None'}
`;
      });
    }

    prompt += `
Please identify any contradictions or conflicts:
1. WITHIN the user's own preferences (e.g., Keto + Vegan)
2. BETWEEN the user and family members (e.g., User: Keto, Family Member: Vegan)
Consider all dietary restrictions and allergies listed above, including less common or new ones. If you are unsure about a restriction, do your best based on the definition provided.

Examples of conflicts:
- Keto + Vegan can be challenging due to limited protein sources
- Dairy-free + Lactose intolerance is redundant
- User wants Keto but family member is Vegan (meal planning conflicts)

Return your response in this JSON format:
{
  "hasConflicts": true/false,
  "conflicts": [
    {
      "items": ["preference1", "preference2"],
      "reason": "explanation of the conflict",
      "severity": "high/medium/low",
      "type": "user" or "family" or "user-family"
    }
  ],
  "suggestions": ["suggestion1", "suggestion2"]
}
Only return the JSON, no additional text.
`;

    try {
      const response = await this.generateContent(prompt);
      // Clean the response to extract JSON from markdown code blocks
      const cleanedResponse = this.extractJsonFromResponse(response);
      return JSON.parse(cleanedResponse);
    } catch (error) {
      console.error('Error detecting dietary conflicts:', error);
      return { hasConflicts: false, conflicts: [], suggestions: [] };
    }
  }

  // Generate meal recommendations based on family profile
  async generateMealRecommendations(familyProfile, availableMeals, goalType = null) {
    // Create a comprehensive profile summary
    const profileSummary = this.createProfileSummary(familyProfile);
    // Create a detailed meal list for the AI to choose from
    const mealList = availableMeals.map((meal, index) => `
    ${index + 1}. "${meal.name}"
       - Calories: ${meal.calories}, Protein: ${meal.protein}g, Carbs: ${meal.carbs}g, Fat: ${meal.fat}g
       - Categories: ${meal.category?.join(', ') || 'N/A'}
       - Meal Types: ${meal.mealType?.join(', ') || 'N/A'}
       - Dietary Tags: ${meal.dietaryTags?.join(', ') || 'N/A'}
       - Allergens: ${meal.allergens?.join(', ') || 'None'}
       - Vegetarian: ${meal.dietType?.isVegetarian ? 'Yes' : 'No'}
       - Vegan: ${meal.dietType?.isVegan ? 'Yes' : 'No'}
       - Gluten-Free: ${meal.dietType?.isGlutenFree ? 'Yes' : 'No'}
       - Dairy-Free: ${meal.dietType?.isDairyFree ? 'Yes' : 'No'}
       - Nut-Free: ${meal.dietType?.isNutFree ? 'Yes' : 'No'}
       - Low-Carb: ${meal.dietType?.isLowCarb ? 'Yes' : 'No'}
       - Keto: ${meal.dietType?.isKeto ? 'Yes' : 'No'}
       - Pescatarian: ${meal.dietType?.isPescatarian ? 'Yes' : 'No'}
       - Halal: ${meal.dietType?.isHalal ? 'Yes' : 'No'}
    `).join('');
    const prompt = `
You are a nutritionist AI helping a Filipino family plan their meals. You MUST ONLY recommend meals from the exact list provided below.
FAMILY PROFILE:
${profileSummary}
${goalType ? `CURRENT GOAL: ${goalType}` : ''}
AVAILABLE MEALS IN DATABASE (you can ONLY choose from these):
${mealList}
IMPORTANT RULES:
1. You MUST ONLY recommend meals from the list above
2. Use the EXACT meal names as written in quotes
3. Recommend 5-8 meals maximum
4. Consider the family's dietary restrictions, allergies, and goals
5. Prioritize meals that match their preferences
Return your response in this JSON format:
{
  "recommendations": [
    {
      "mealName": "EXACT meal name from the list",
      "reason": "why this meal is recommended for this family",
      "nutritionalBenefits": "key nutritional benefits",
      "suitability": "how it fits the family profile"
    }
  ],
  "generalAdvice": "overall dietary advice for this family"
}
Only return the JSON, no additional text. Remember: ONLY use meal names that appear in the provided list.
`;
    try {
      const response = await this.generateContent(prompt);
      const cleanedResponse = this.extractJsonFromResponse(response);
      const aiResponse = JSON.parse(cleanedResponse);
      // Validate that all recommended meals exist in the database
      const validatedRecommendations = aiResponse.recommendations.filter(rec => {
        const mealExists = availableMeals.some(meal =>
          meal.name.toLowerCase() === rec.mealName.toLowerCase()
        );
        if (!mealExists) {
          console.warn(`AI recommended non-existent meal: ${rec.mealName}`);
        }
        return mealExists;
      });
      return {
        recommendations: validatedRecommendations,
        generalAdvice: aiResponse.generalAdvice || ''
      };
    } catch (error) {
      console.error('Error generating meal recommendations:', error);
      return { recommendations: [], generalAdvice: '' };
    }
  }

  // Generate goal-based dietary suggestions
  async generateGoalBasedSuggestions(goal, healthCondition = null) {
    let prompt = `
You are a nutritionist AI. A user has selected the goal: "${goal}".
${healthCondition ? `They also have the health condition: "${healthCondition}".` : ''}
Based on this goal${healthCondition ? ' and health condition' : ''}, suggest appropriate dietary preferences from ONLY these available options in our app:
Available Dietary Restrictions: ${this.AVAILABLE_DIETARY_RESTRICTIONS.join(', ')}
Available Allergies to Avoid: ${this.AVAILABLE_ALLERGIES.join(', ')}
IMPORTANT: You can ONLY recommend dietary restrictions and allergies from the lists above. Do not suggest any options that are not in these lists.
Provide specific recommendations and explain why each suggestion is beneficial for their goal${healthCondition ? ' and condition' : ''}.
Return your response in this JSON format:
{
  "recommendedRestrictions": ["restriction1", "restriction2"],
  "recommendedAllergies": ["allergy1", "allergy2"],
  "explanation": "detailed explanation of why these are recommended",
  "additionalTips": ["tip1", "tip2", "tip3"]
}
Only return the JSON, no additional text. Remember: ONLY use options from the provided lists.
`;
    try {
      const response = await this.generateContent(prompt);
      const cleanedResponse = this.extractJsonFromResponse(response);
      return JSON.parse(cleanedResponse);
    } catch (error) {
      console.error('Error generating goal-based suggestions:', error);
      return {
        recommendedRestrictions: [],
        recommendedAllergies: [],
        explanation: '',
        additionalTips: []
      };
    }
  }

  // Create a comprehensive profile summary
  createProfileSummary(familyProfile) {
    const { dietaryPreferences, familyMembers } = familyProfile;

    let summary = `
DIETARY PREFERENCES:
- Restrictions: ${dietaryPreferences?.restrictions?.join(', ') || 'None'}
- Allergies: ${dietaryPreferences?.allergies?.join(', ') || 'None'}
- Disliked Ingredients: ${dietaryPreferences?.dislikedIngredients?.join(', ') || 'None'}
- Daily Calorie Target: ${dietaryPreferences?.calorieTarget || 'Not specified'}
- Meals Per Day: ${dietaryPreferences?.mealFrequency || 3}
`;
    if (familyMembers && familyMembers.length > 0) {
      summary += `\n    FAMILY MEMBERS:`;
      familyMembers.forEach((member, index) => {
        const age = member.dateOfBirth ? this.calculateAge(member.dateOfBirth) : 'Unknown';
        summary += `\n    ${index + 1}. ${member.name} (Age: ${age})`;
        if (member.dietaryPreferences) {
          summary += `\n       - Restrictions: ${member.dietaryPreferences.restrictions?.join(', ') || 'None'}`;
          summary += `\n       - Allergies: ${member.dietaryPreferences.allergies?.join(', ') || 'None'}`;
          summary += `\n       - Calorie Target: ${member.dietaryPreferences.calorieTarget || 'Not specified'}`;
        }
      });
    }
    return summary;
  }

  // Helper function to calculate age
  calculateAge(dateOfBirth) {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  }

  // Generate meal replacement recommendations
  async generateMealReplacement(originalMeal, familyProfile, availableMeals, mealType) {
    const profileSummary = this.createProfileSummary(familyProfile);

    // Create a detailed meal list for the AI to choose from
    const mealList = availableMeals.map((meal, index) => `
    ${index + 1}. "${meal.name}"
       - Calories: ${meal.calories}, Protein: ${meal.protein}g, Carbs: ${meal.carbs}g, Fat: ${meal.fat}g
       - Categories: ${meal.category?.join(', ') || 'N/A'}
       - Meal Types: ${meal.mealType?.join(', ') || 'N/A'}
       - Dietary Tags: ${meal.dietaryTags?.join(', ') || 'N/A'}
       - Allergens: ${meal.allergens?.join(', ') || 'None'}
       - Vegetarian: ${meal.dietType?.isVegetarian ? 'Yes' : 'No'}
       - Vegan: ${meal.dietType?.isVegan ? 'Yes' : 'No'}
       - Gluten-Free: ${meal.dietType?.isGlutenFree ? 'Yes' : 'No'}
       - Dairy-Free: ${meal.dietType?.isDairyFree ? 'Yes' : 'No'}
       - Nut-Free: ${meal.dietType?.isNutFree ? 'Yes' : 'No'}
       - Low-Carb: ${meal.dietType?.isLowCarb ? 'Yes' : 'No'}
       - Keto: ${meal.dietType?.isKeto ? 'Yes' : 'No'}
    `).join('');

    const prompt = `
You are a nutritionist AI helping replace a meal that no longer fits a family's dietary preferences.

FAMILY PROFILE:
${profileSummary}

MEAL TO REPLACE:
"${originalMeal.name}" (${mealType})
- Calories: ${originalMeal.calories}, Protein: ${originalMeal.protein}g, Carbs: ${originalMeal.carbs}g, Fat: ${originalMeal.fat}g
- Current dietary tags: ${originalMeal.dietaryTags?.join(', ') || 'None'}

AVAILABLE REPLACEMENT MEALS (you can ONLY choose from these):
${mealList}

TASK: Find the BEST replacement meal that:
1. Fits the family's NEW dietary preferences
2. Is similar in nutritional value to the original meal
3. Is appropriate for ${mealType}
4. Comes from the provided list ONLY

Return your response in this JSON format:
{
  "replacement": {
    "mealName": "EXACT meal name from the list",
    "reason": "why this is the best replacement",
    "nutritionalComparison": "how it compares nutritionally to the original",
    "suitability": "how it fits the new dietary preferences"
  },
  "alternatives": [
    {
      "mealName": "EXACT meal name from the list",
      "reason": "why this is also a good option"
    }
  ]
}

Only return the JSON, no additional text. Remember: ONLY use meal names that appear in the provided list.
`;

    try {
      const response = await this.generateContent(prompt);
      const cleanedResponse = this.extractJsonFromResponse(response);
      const aiResponse = JSON.parse(cleanedResponse);

      // Validate that the recommended meal exists
      if (aiResponse.replacement) {
        const mealExists = availableMeals.some(meal =>
          meal.name.toLowerCase() === aiResponse.replacement.mealName.toLowerCase()
        );
        if (!mealExists) {
          console.warn(`AI recommended non-existent replacement meal: ${aiResponse.replacement.mealName}`);
          return null;
        }
      }

      return aiResponse;
    } catch (error) {
      console.error('Error generating meal replacement:', error);
      return null;
    }
  }

  // Helper function to extract JSON from markdown code blocks
  extractJsonFromResponse(response) {
    // Remove markdown code block markers
    let cleaned = response.replace(/```json\s*/g, '').replace(/```\s*/g, '');
    // Try to find JSON object boundaries
    const jsonStart = cleaned.indexOf('{');
    const jsonEnd = cleaned.lastIndexOf('}');
    if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
      cleaned = cleaned.substring(jsonStart, jsonEnd + 1);
    }
    return cleaned.trim();
  }

  // Generate a complete meal plan for a specific date
  async generateAIMealPlan(familyProfile, availableMeals, targetDate = null) {
    const profileSummary = this.createProfileSummary(familyProfile);

    // Create a detailed meal list for the AI to choose from
    const mealList = availableMeals.map((meal, index) => `
    ${index + 1}. "${meal.name}"
       - Calories: ${meal.calories}, Protein: ${meal.protein}g, Carbs: ${meal.carbs}g, Fat: ${meal.fat}g
       - Categories: ${meal.category?.join(', ') || 'N/A'}
       - Meal Types: ${meal.mealType?.join(', ') || 'N/A'}
       - Dietary Tags: ${meal.dietaryTags?.join(', ') || 'N/A'}
       - Allergens: ${meal.allergens?.join(', ') || 'None'}
       - Vegetarian: ${meal.dietType?.isVegetarian ? 'Yes' : 'No'}
       - Vegan: ${meal.dietType?.isVegan ? 'Yes' : 'No'}
       - Gluten-Free: ${meal.dietType?.isGlutenFree ? 'Yes' : 'No'}
       - Dairy-Free: ${meal.dietType?.isDairyFree ? 'Yes' : 'No'}
       - Nut-Free: ${meal.dietType?.isNutFree ? 'Yes' : 'No'}
       - Low-Carb: ${meal.dietType?.isLowCarb ? 'Yes' : 'No'}
       - Keto: ${meal.dietType?.isKeto ? 'Yes' : 'No'}
       - Pescatarian: ${meal.dietType?.isPescatarian ? 'Yes' : 'No'}
       - Halal: ${meal.dietType?.isHalal ? 'Yes' : 'No'}
    `).join('');

    const prompt = `
You are a nutritionist AI creating a complete daily meal plan for a Filipino family. You MUST ONLY use meals from the exact list provided below.

FAMILY PROFILE:
${profileSummary}

AVAILABLE MEALS IN DATABASE (you can ONLY choose from these):
${mealList}

Create a balanced daily meal plan with:
- 1-2 breakfast options
- 1-2 lunch options
- 1-2 dinner options

IMPORTANT REQUIREMENTS:
1. ONLY use meal names that appear EXACTLY in the provided list
2. Consider the family's dietary restrictions and allergies
3. Ensure nutritional balance across the day
4. Respect calorie targets if specified
5. Choose meals appropriate for each meal type (breakfast, lunch, dinner)
6. Provide variety and flavor balance

Return your response in this JSON format:
{
  "mealPlan": {
    "breakfast": [
      {
        "mealName": "exact meal name from list",
        "reason": "why this meal is good for breakfast and fits their preferences"
      }
    ],
    "lunch": [
      {
        "mealName": "exact meal name from list",
        "reason": "why this meal is good for lunch and fits their preferences"
      }
    ],
    "dinner": [
      {
        "mealName": "exact meal name from list",
        "reason": "why this meal is good for dinner and fits their preferences"
      }
    ]
  },
  "nutritionalSummary": "brief summary of the nutritional balance",
  "personalizedMessage": "friendly message explaining why this meal plan was created for them"
}

Only return the JSON, no additional text. Remember: ONLY use meal names that appear in the provided list.
`;

    try {
      const response = await this.generateContent(prompt);
      const cleanedResponse = this.extractJsonFromResponse(response);
      const aiResponse = JSON.parse(cleanedResponse);

      // Validate that all recommended meals exist in the database
      const validateMealPlan = (mealPlan) => {
        const validatedPlan = {};

        ['breakfast', 'lunch', 'dinner'].forEach(mealType => {
          if (mealPlan[mealType]) {
            validatedPlan[mealType] = mealPlan[mealType].filter(mealItem => {
              const mealExists = availableMeals.some(meal =>
                meal.name.toLowerCase() === mealItem.mealName.toLowerCase()
              );
              if (!mealExists) {
                console.warn(`AI recommended non-existent meal: ${mealItem.mealName}`);
              }
              return mealExists;
            });
          }
        });

        return validatedPlan;
      };

      const validatedMealPlan = validateMealPlan(aiResponse.mealPlan);

      return {
        mealPlan: validatedMealPlan,
        nutritionalSummary: aiResponse.nutritionalSummary || '',
        personalizedMessage: aiResponse.personalizedMessage || 'Here\'s your personalized meal plan!'
      };
    } catch (error) {
      console.error('Error generating AI meal plan:', error);
      return {
        mealPlan: { breakfast: [], lunch: [], dinner: [] },
        nutritionalSummary: '',
        personalizedMessage: 'I\'m sorry, I had trouble creating your meal plan. Please try again.'
      };
    }
  }

  // Aggregate family preferences and detect conflicts
  aggregateFamilyPreferences(familyProfile) {
    const { dietaryPreferences: userPrefs, familyMembers } = familyProfile;

    // Collect all preferences
    const allRestrictions = [...(userPrefs?.restrictions || [])];
    const allAllergies = [...(userPrefs?.allergies || [])];
    const allDislikedIngredients = [...(userPrefs?.dislikedIngredients || [])];
    const calorieTargets = [];

    // Add user's calorie target if available
    if (userPrefs?.calorieTarget) {
      calorieTargets.push(userPrefs.calorieTarget);
    }

    // Aggregate family member preferences
    familyMembers.forEach(member => {
      if (member.dietaryPreferences) {
        allRestrictions.push(...(member.dietaryPreferences.restrictions || []));
        allAllergies.push(...(member.dietaryPreferences.allergies || []));
        allDislikedIngredients.push(...(member.dietaryPreferences.dislikedIngredients || []));

        if (member.dietaryPreferences.calorieTarget) {
          calorieTargets.push(member.dietaryPreferences.calorieTarget);
        }
      }
    });

    // Remove duplicates and calculate average calorie target
    const uniqueRestrictions = [...new Set(allRestrictions)];
    const uniqueAllergies = [...new Set(allAllergies)];
    const uniqueDislikedIngredients = [...new Set(allDislikedIngredients)];
    const averageCalorieTarget = calorieTargets.length > 0
      ? Math.round(calorieTargets.reduce((sum, cal) => sum + cal, 0) / calorieTargets.length)
      : 2000; // Default if no targets specified

    // Detect conflicts between dietary restrictions
    const conflicts = [];
    const conflictingPairs = [
      ['Vegetarian', 'Pescatarian'],
      ['Vegan', 'Vegetarian'],
      ['Vegan', 'Pescatarian'],
      ['Keto', 'Low-Carb'], // Not really a conflict, but worth noting
    ];

    conflictingPairs.forEach(([restriction1, restriction2]) => {
      if (uniqueRestrictions.includes(restriction1) && uniqueRestrictions.includes(restriction2)) {
        conflicts.push(`Family has both ${restriction1} and ${restriction2} preferences`);
      }
    });

    // Check for allergy-restriction conflicts
    if (uniqueAllergies.includes('Milk') && uniqueRestrictions.includes('Vegetarian')) {
      conflicts.push('Milk allergy detected with Vegetarian preference - will prioritize dairy-free options');
    }

    return {
      aggregatedPreferences: {
        restrictions: uniqueRestrictions,
        allergies: uniqueAllergies,
        dislikedIngredients: uniqueDislikedIngredients,
        calorieTarget: averageCalorieTarget
      },
      conflicts,
      familySize: familyMembers.length + 1 // +1 for the user
    };
  }

  // Generate family meal plan with conflict detection
  async generateFamilyMealPlan(familyProfile, availableMeals, targetDate = null) {
    const { aggregatedPreferences, conflicts, familySize } = this.aggregateFamilyPreferences(familyProfile);

    // Create a detailed meal list for the AI to choose from
    const mealList = availableMeals.map((meal, index) => `
    ${index + 1}. "${meal.name}"
       - Calories: ${meal.calories}, Protein: ${meal.protein}g, Carbs: ${meal.carbs}g, Fat: ${meal.fat}g
       - Categories: ${meal.category?.join(', ') || 'N/A'}
       - Meal Types: ${meal.mealType?.join(', ') || 'N/A'}
       - Dietary Tags: ${meal.dietaryTags?.join(', ') || 'N/A'}
       - Allergens: ${meal.allergens?.join(', ') || 'None'}
       - Vegetarian: ${meal.dietType?.isVegetarian ? 'Yes' : 'No'}
    `).join('\n');

    const conflictInfo = conflicts.length > 0
      ? `\n\nIMPORTANT DIETARY CONFLICTS DETECTED:\n${conflicts.join('\n')}\nPlease accommodate these conflicts in your meal selection.`
      : '';

    const prompt = `
You are a Filipino meal planning assistant creating a family meal plan for ${familySize} people.

FAMILY DIETARY PROFILE:
- Dietary Restrictions: ${aggregatedPreferences.restrictions.join(', ') || 'None'}
- Allergies: ${aggregatedPreferences.allergies.join(', ') || 'None'}
- Disliked Ingredients: ${aggregatedPreferences.dislikedIngredients.join(', ') || 'None'}
- Average Daily Calorie Target: ${aggregatedPreferences.calorieTarget} calories
- Family Size: ${familySize} people${conflictInfo}

AVAILABLE MEALS TO CHOOSE FROM:
${mealList}

INSTRUCTIONS:
1. Create a balanced daily meal plan with breakfast, lunch, and dinner
2. ONLY select meals from the provided list above
3. STRICTLY follow ALL family dietary restrictions and allergies - NO EXCEPTIONS
4. If family has VEGAN restrictions, NEVER include any animal products (meat, dairy, eggs, fish)
5. If family has VEGETARIAN restrictions, avoid meat and fish but dairy/eggs may be okay unless allergies exist
6. NEVER include meals with allergens listed in the family profile
7. Aim for the average calorie target across all meals
8. Provide variety in meal types and nutritional balance
9. If conflicts exist, prioritize safety (allergies) over preferences, then choose the most restrictive diet
10. Include 1-2 meals per meal type (breakfast, lunch, dinner)

Return your response in this EXACT JSON format:
{
  "mealPlan": {
    "breakfast": [
      {
        "mealName": "Exact meal name from the list",
        "reason": "Why this meal works for the family (dietary compatibility, nutrition, etc.)"
      }
    ],
    "lunch": [
      {
        "mealName": "Exact meal name from the list",
        "reason": "Why this meal works for the family"
      }
    ],
    "dinner": [
      {
        "mealName": "Exact meal name from the list",
        "reason": "Why this meal works for the family"
      }
    ]
  },
  "personalizedMessage": "A warm, personalized message about this family meal plan",
  "nutritionalSummary": "Brief summary of nutritional benefits and how it meets family needs"
}
`;

    try {
      const response = await this.generateContent(prompt);
      const cleanedResponse = this.extractJsonFromResponse(response);
      const aiResponse = JSON.parse(cleanedResponse);

      // Validate that all recommended meals exist in the database
      const validateMealPlan = (mealPlan) => {
        const validatedPlan = {};

        ['breakfast', 'lunch', 'dinner'].forEach(mealType => {
          if (mealPlan[mealType]) {
            validatedPlan[mealType] = mealPlan[mealType].filter(mealItem => {
              const mealExists = availableMeals.some(meal =>
                meal.name.toLowerCase() === mealItem.mealName.toLowerCase()
              );
              if (!mealExists) {
                console.warn(`AI recommended non-existent meal: ${mealItem.mealName}`);
              }
              return mealExists;
            });
          }
        });

        return validatedPlan;
      };

      const validatedMealPlan = validateMealPlan(aiResponse.mealPlan);

      return {
        mealPlan: validatedMealPlan,
        nutritionalSummary: aiResponse.nutritionalSummary || '',
        personalizedMessage: aiResponse.personalizedMessage || 'Here\'s your personalized family meal plan!',
        conflicts: conflicts
      };
    } catch (error) {
      console.error('Error generating family meal plan:', error);
      return {
        mealPlan: { breakfast: [], lunch: [], dinner: [] },
        nutritionalSummary: '',
        personalizedMessage: 'I\'m sorry, I had trouble creating your family meal plan. Please try again.',
        conflicts: conflicts
      };
    }
  }

  // Generate chat response for general queries
  async generateChatResponse(message, context = {}) {
    let availableMealsContext = '';
    // If context includes available meals, add them to the prompt
    if (context.availableMeals && context.availableMeals.length > 0) {
      const mealNames = context.availableMeals.map(meal => meal.name).join(', ');
      availableMealsContext = `\n\nAvailable meals in our database: ${mealNames}`;
    }
    const prompt = `
You are a helpful Filipino meal planning assistant. The user has sent you this message: "${message}"
${context.familyProfile ? `User's Family Profile: ${this.createProfileSummary(context.familyProfile)}` : ''}
${availableMealsContext}
Provide a helpful, friendly response related to meal planning, nutrition, or Filipino cuisine. Keep your response concise and actionable.
IMPORTANT: If the user asks about meal recommendations or specific dishes, ONLY mention meals that are available in our database (listed above).
If the user is asking about meal recommendations, dietary advice, or nutrition, provide specific suggestions from our available meals.
If the user is asking about Filipino dishes, provide cultural context and preparation tips, but focus on dishes we have available.
Return your response as plain text, maximum 200 words.
`;
    try {
      const response = await this.generateContent(prompt);
      return response;
    } catch (error) {
      console.error('Error generating chat response:', error);
      return "I'm sorry, I'm having trouble responding right now. Please try again later.";
    }
  }

  // Filter meals based on dietary preferences
  filterMealsByDietaryPreferences(availableMeals, dietaryPreferences) {
    if (!dietaryPreferences || !availableMeals || availableMeals.length === 0) {
      return availableMeals || [];
    }

    return availableMeals.filter(meal => {
      // Check dietary restrictions (inclusive filtering - user wants ONLY these types)
      if (dietaryPreferences.restrictions && dietaryPreferences.restrictions.length > 0) {
        const dietType = meal.dietType || {};
        const hasRequiredRestriction = dietaryPreferences.restrictions.some(restriction => {
          switch (restriction) {
            case 'Vegetarian':
              return dietType.isVegetarian === true;
            case 'Vegan':
              return dietType.isVegan === true;
            case 'Gluten-Free':
              return dietType.isGlutenFree === true;
            case 'Dairy-Free':
              return dietType.isDairyFree === true;
            case 'Nut-Free':
              return dietType.isNutFree === true;
            case 'Low-Carb':
              return dietType.isLowCarb === true;
            case 'Keto':
              return dietType.isKeto === true;
            case 'High-Protein':
              return dietType.isHighProtein === true;
            case 'Low-Sodium':
              return dietType.isLowSodium === true;
            case 'Halal':
              return dietType.isHalal === true;
            case 'Pescatarian':
              return dietType.isPescatarian === true;
            default:
              return false;
          }
        });

        if (!hasRequiredRestriction) {
          return false;
        }
      }

      // Check allergies (exclusive filtering - user does NOT want these)
      if (dietaryPreferences.allergies && dietaryPreferences.allergies.length > 0) {
        const mealAllergens = meal.allergens || [];
        const mealIngredients = meal.ingredients || [];

        const hasAllergen = dietaryPreferences.allergies.some(allergen => {
          // Check if allergen is in the meal's allergen list
          if (mealAllergens.some(mealAllergen =>
            mealAllergen.toLowerCase().includes(allergen.toLowerCase())
          )) {
            return true;
          }

          // Check if allergen is in the ingredients
          if (mealIngredients.some(ingredient =>
            ingredient.toLowerCase().includes(allergen.toLowerCase())
          )) {
            return true;
          }

          return false;
        });

        if (hasAllergen) {
          return false;
        }
      }

      // Check disliked ingredients (exclusive filtering)
      if (dietaryPreferences.dislikedIngredients && dietaryPreferences.dislikedIngredients.length > 0) {
        const mealIngredients = meal.ingredients || [];

        const hasDislikedIngredient = dietaryPreferences.dislikedIngredients.some(disliked => {
          return mealIngredients.some(ingredient =>
            ingredient.toLowerCase().includes(disliked.toLowerCase())
          );
        });

        if (hasDislikedIngredient) {
          return false;
        }
      }

      return true;
    });
  }

  // Filter meals based on dietary preferences
  filterMealsByDietaryPreferences(availableMeals, dietaryPreferences) {
    if (!dietaryPreferences || !availableMeals || availableMeals.length === 0) {
      return availableMeals || [];
    }

    return availableMeals.filter(meal => {
      // Check dietary restrictions (inclusive filtering - user wants ONLY these types)
      if (dietaryPreferences.restrictions && dietaryPreferences.restrictions.length > 0) {
        const dietType = meal.dietType || {};
        const hasRequiredRestriction = dietaryPreferences.restrictions.some(restriction => {
          switch (restriction) {
            case 'Vegetarian':
              return dietType.isVegetarian === true;
            case 'Vegan':
              return dietType.isVegan === true;
            case 'Gluten-Free':
              return dietType.isGlutenFree === true;
            case 'Dairy-Free':
              return dietType.isDairyFree === true;
            case 'Nut-Free':
              return dietType.isNutFree === true;
            case 'Low-Carb':
              return dietType.isLowCarb === true;
            case 'Keto':
              return dietType.isKeto === true;
            case 'High-Protein':
              return dietType.isHighProtein === true;
            case 'Low-Sodium':
              return dietType.isLowSodium === true;
            case 'Halal':
              return dietType.isHalal === true;
            case 'Pescatarian':
              return dietType.isPescatarian === true;
            default:
              return false;
          }
        });

        if (!hasRequiredRestriction) {
          return false;
        }
      }

      // Check allergies (exclusive filtering - user does NOT want these)
      if (dietaryPreferences.allergies && dietaryPreferences.allergies.length > 0) {
        const mealAllergens = meal.allergens || [];
        const mealIngredients = meal.ingredients || [];

        const hasAllergen = dietaryPreferences.allergies.some(allergen => {
          // Check if allergen is in the meal's allergen list
          if (mealAllergens.some(mealAllergen =>
            mealAllergen.toLowerCase().includes(allergen.toLowerCase())
          )) {
            return true;
          }

          // Check if allergen is in the ingredients
          if (mealIngredients.some(ingredient =>
            ingredient.toLowerCase().includes(allergen.toLowerCase())
          )) {
            return true;
          }

          return false;
        });

        if (hasAllergen) {
          return false;
        }
      }

      // Check disliked ingredients (exclusive filtering)
      if (dietaryPreferences.dislikedIngredients && dietaryPreferences.dislikedIngredients.length > 0) {
        const mealIngredients = meal.ingredients || [];

        const hasDislikedIngredient = dietaryPreferences.dislikedIngredients.some(disliked => {
          return mealIngredients.some(ingredient =>
            ingredient.toLowerCase().includes(disliked.toLowerCase())
          );
        });

        if (hasDislikedIngredient) {
          return false;
        }
      }

      return true;
    });
  }

  // Edit existing meal plan based on user request
  async editMealPlan(userProfile, availableMeals, currentMealPlan, editRequest) {
    try {
      const { dietaryPreferences, familyMembers } = userProfile;

      // Filter meals based on dietary preferences
      const filteredMeals = this.filterMealsByDietaryPreferences(availableMeals, dietaryPreferences);

      // Create meal options string
      const mealOptions = filteredMeals.map(meal =>
        `${meal.name} (${meal.dietType || 'General'}) - ₱${meal.price} - ${meal.description || 'No description'}`
      ).join('\n');

      const prompt = `You are a Filipino meal planning assistant. A user wants to edit their current meal plan.

CURRENT MEAL PLAN:
${JSON.stringify(currentMealPlan, null, 2)}

USER'S EDIT REQUEST:
${editRequest}

USER DIETARY PREFERENCES:
- Restrictions: ${dietaryPreferences?.restrictions?.join(', ') || 'None'}
- Allergies: ${dietaryPreferences?.allergies?.join(', ') || 'None'}
- Disliked Ingredients: ${dietaryPreferences?.dislikedIngredients?.join(', ') || 'None'}
- Daily Calorie Target: ${dietaryPreferences?.calorieTarget || 'Not specified'}
- Meals Per Day: ${dietaryPreferences?.mealFrequency || 3}

${familyMembers && familyMembers.length > 0 ? `
FAMILY MEMBERS:
${familyMembers.map(member => `- ${member.name} (${member.relationship}):
  Restrictions: ${member.dietaryPreferences?.restrictions?.join(', ') || 'None'}
  Allergies: ${member.dietaryPreferences?.allergies?.join(', ') || 'None'}`).join('\n')}
` : ''}

AVAILABLE MEALS TO CHOOSE FROM:
${mealOptions}

Please generate an updated meal plan that incorporates the user's requested changes. You must:
1. Only use meals from the available meals list above
2. Respect all dietary preferences and restrictions
3. Make the specific changes requested by the user
4. Keep the same structure as the current meal plan
5. Provide a brief explanation of what changes were made

Return your response in this exact JSON format:
{
  "mealPlan": {
    "breakfast": [{"name": "...", "price": number, "description": "...", "calories": number, "protein": number, "carbs": number, "fat": number}],
    "lunch": [{"name": "...", "price": number, "description": "...", "calories": number, "protein": number, "carbs": number, "fat": number}],
    "dinner": [{"name": "...", "price": number, "description": "...", "calories": number, "protein": number, "carbs": number, "fat": number}]
  },
  "nutritionalSummary": "Brief nutritional overview of the updated plan",
  "personalizedMessage": "Friendly explanation of what changes were made and why",
  "totalCost": number
}`;

      const result = await this.generateContent(prompt);

      // Parse the JSON response
      const jsonMatch = result.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in AI response');
      }

      const parsedResult = JSON.parse(jsonMatch[0]);

      // Validate the response structure
      if (!parsedResult.mealPlan || !parsedResult.personalizedMessage) {
        throw new Error('Invalid meal plan structure in AI response');
      }

      return parsedResult;
    } catch (error) {
      console.error('Error editing meal plan:', error);
      throw new Error(`Failed to edit meal plan: ${error.message}`);
    }
  }
}

module.exports = new GeminiService();
