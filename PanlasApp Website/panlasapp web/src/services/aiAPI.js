import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const aiAPI = axios.create({
  baseURL: `${API_BASE_URL}/ai`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
aiAPI.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// AI API functions
export const detectDietaryConflicts = async (preferences) => {
  try {
    const response = await aiAPI.post('/dietary-conflicts', preferences);
    return response.data;
  } catch (error) {
    console.error('Error detecting dietary conflicts:', error);
    throw error;
  }
};

export const getMealRecommendations = async (data) => {
  try {
    const response = await aiAPI.post('/meal-recommendations', data);
    return response.data;
  } catch (error) {
    console.error('Error getting meal recommendations:', error);
    throw error;
  }
};

export const getGoalSuggestions = async (data) => {
  try {
    const response = await aiAPI.post('/goal-suggestions', data);
    return response.data;
  } catch (error) {
    console.error('Error getting goal suggestions:', error);
    throw error;
  }
};

export const sendChatMessage = async (data) => {
  try {
    const response = await aiAPI.post('/chat', data);
    return response.data;
  } catch (error) {
    console.error('Error sending chat message:', error);
    throw error;
  }
};

export const generateAIMealPlan = async () => {
  try {
    const response = await aiAPI.post('/generate-meal-plan');
    return response.data;
  } catch (error) {
    console.error('Error generating AI meal plan:', error);
    throw error;
  }
};

export const generateFamilyMealPlan = async () => {
  try {
    const response = await aiAPI.post('/generate-family-meal-plan');
    return response.data;
  } catch (error) {
    console.error('Error generating family meal plan:', error);
    throw error;
  }
};

export const getGoalsAndConditions = async () => {
  try {
    const response = await aiAPI.get('/goals');
    return response.data;
  } catch (error) {
    console.error('Error getting goals and conditions:', error);
    throw error;
  }
};

export default {
  detectDietaryConflicts,
  getMealRecommendations,
  getGoalSuggestions,
  sendChatMessage,
  generateAIMealPlan,
  generateFamilyMealPlan,
  getGoalsAndConditions,
};
