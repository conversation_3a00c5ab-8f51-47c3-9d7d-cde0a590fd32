import React, { useState, useEffect, useRef } from "react";
import Layout from "../Layout/Layout";
import aiAPI from "../../services/aiAPI";
import "./Chat.css";

const Chat = () => {
  const [messages, setMessages] = useState([]);
  const [inputText, setInputText] = useState('');
  const [loading, setLoading] = useState(false);
  const [selectedGoal, setSelectedGoal] = useState(null);
  const [selectedHealthCondition, setSelectedHealthCondition] = useState(null);
  const [selectedBudget, setSelectedBudget] = useState(null);
  const [showGoalSelection, setShowGoalSelection] = useState(true);
  const [goals, setGoals] = useState([]);
  const [healthConditions, setHealthConditions] = useState([]);
  const [showBudgetSelection, setShowBudgetSelection] = useState(false);
  const [generatedMealPlan, setGeneratedMealPlan] = useState(null);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState('');
  const [isEditingMode, setIsEditingMode] = useState(false);

  const messagesEndRef = useRef(null);

  const budgetRanges = [
    { id: 'low', name: 'Low Range', description: 'Affordable, budget-friendly meals' },
    { id: 'mid', name: 'Mid Range', description: 'Balanced cost and quality meals' },
    { id: 'high', name: 'High Range', description: 'Premium ingredients and meals' }
  ];

  const defaultGoals = [
    { id: 'lose_weight', name: 'Lose Weight', description: 'Get meal suggestions to help with weight loss' },
    { id: 'build_muscle', name: 'Build Muscle', description: 'Get high-protein meals to support muscle building' },
    { id: 'manage_health', name: 'Manage a Health Condition', description: 'Get meals tailored to specific health conditions' },
    { id: 'eat_sustainably', name: 'Eat Sustainably', description: 'Get environmentally conscious meal suggestions' },
    { id: 'budget', name: 'Budget', description: 'Get meal suggestions based on your budget' },
    { id: 'generate_meal_plan', name: 'Generate a meal plan', description: 'Create a personalized daily meal plan based on your dietary preferences' },
    { id: 'generate_family_meal_plan', name: 'Generate a meal plan for family', description: 'Create a meal plan considering your family members\' dietary preferences and allergies' },
    { id: 'other', name: 'Other', description: 'Chat directly with AI for custom dietary advice' }
  ];

  const defaultHealthConditions = [
    { id: 'type2_diabetes', name: 'Type 2 Diabetes', description: 'Low-sugar, low-carb meal recommendations' },
    { id: 'celiac_disease', name: 'Celiac Disease', description: 'Gluten-free meal recommendations' },
    { id: 'hypertension', name: 'Hypertension', description: 'Low-sodium meal recommendations' },
    { id: 'heart_disease', name: 'Heart Disease', description: 'Heart-healthy, low-cholesterol meals' },
    { id: 'lactose_intolerance', name: 'Lactose Intolerance', description: 'Dairy-free meal recommendations' }
  ];

  useEffect(() => {
    loadGoalsAndConditions();
    addWelcomeMessage();
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const loadGoalsAndConditions = async () => {
    try {
      const response = await aiAPI.getGoalsAndConditions();
      if (response.success) {
        setGoals(response.goals || defaultGoals);
        setHealthConditions(response.healthConditions || defaultHealthConditions);
      } else {
        setGoals(defaultGoals);
        setHealthConditions(defaultHealthConditions);
      }
    } catch (error) {
      console.error('Error loading goals:', error);
      setGoals(defaultGoals);
      setHealthConditions(defaultHealthConditions);
    }
  };

  const addWelcomeMessage = () => {
    const welcomeMessage = {
      id: Date.now(),
      text: "Hello! I'm your AI meal planning assistant. To get started, please select one of your health goals below, and I'll provide personalized dietary recommendations for you.",
      isUser: false,
      timestamp: new Date(),
    };
    setMessages([welcomeMessage]);
  };

  const selectGoal = async (goal) => {
    setSelectedGoal(goal);
    setShowGoalSelection(false);

    const goalMessage = {
      id: Date.now(),
      text: goal.id === 'other'
        ? 'I want to chat about something else'
        : goal.id === 'budget'
        ? 'I want to plan meals based on my budget'
        : goal.id === 'generate_meal_plan'
        ? 'I want to generate a meal plan'
        : goal.id === 'generate_family_meal_plan'
        ? 'I want to generate a meal plan for family'
        : `I want to ${goal.name.toLowerCase()}`,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, goalMessage]);

    if (goal.id === 'other') {
      const aiMessage = {
        id: Date.now() + 1,
        text: "I'm here to help with any dietary questions or meal planning needs you have! Please tell me what you'd like to know about nutrition, meals, or healthy eating.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, aiMessage]);
    } else if (goal.id === 'manage_health') {
      const responseMessage = {
        id: Date.now() + 1,
        text: "Great! Please select the specific health condition you'd like to manage:",
        isUser: false,
        timestamp: new Date(),
        showHealthConditions: true,
      };
      setMessages(prev => [...prev, responseMessage]);
    } else if (goal.id === 'budget') {
      setShowBudgetSelection(true);
    } else if (goal.id === 'generate_meal_plan') {
      await generateAIMealPlan();
    } else if (goal.id === 'generate_family_meal_plan') {
      await generateFamilyMealPlan();
    } else {
      await getGoalSuggestions(goal);
    }
  };

  const selectHealthCondition = async (condition) => {
    setSelectedHealthCondition(condition);

    const conditionMessage = {
      id: Date.now(),
      text: `I want to manage ${condition.name}`,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, conditionMessage]);

    await getGoalSuggestions(selectedGoal, condition);
  };

  const selectBudgetRange = async (budget) => {
    setSelectedBudget(budget);
    setShowBudgetSelection(false);

    const budgetMessage = {
      id: Date.now(),
      text: `My budget is: ${budget.name}`,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, budgetMessage]);
    await getGoalSuggestions(selectedGoal, null, budget);
  };

  const getGoalSuggestions = async (goal, healthCondition = null, budget = null) => {
    try {
      setLoading(true);

      const response = await aiAPI.getGoalSuggestions({
        goal: goal.name,
        healthCondition: healthCondition?.name,
        budget: budget?.id
      });

      if (response.success && response.suggestions) {
        const suggestions = response.suggestions;
        let responseText = suggestions.explanation;

        if (suggestions.recommendedRestrictions.length > 0) {
          responseText += `\n\n🥗 Recommended Dietary Restrictions:\n${suggestions.recommendedRestrictions.map(r => `• ${r}`).join('\n')}`;
        }

        if (suggestions.recommendedAllergies.length > 0) {
          responseText += `\n\n⚠️ Consider avoiding:\n${suggestions.recommendedAllergies.map(a => `• ${a}`).join('\n')}`;
        }

        if (suggestions.additionalTips.length > 0) {
          responseText += `\n\n💡 Additional Tips:\n${suggestions.additionalTips.map(tip => `• ${tip}`).join('\n')}`;
        }

        responseText += `\n\nWould you like me to recommend specific meals from our database that match these preferences?`;

        const aiMessage = {
          id: Date.now(),
          text: responseText,
          isUser: false,
          timestamp: new Date(),
        };

        setMessages(prev => [...prev, aiMessage]);
      }
    } catch (error) {
      console.error('Error getting goal suggestions:', error);
      const errorMessage = {
        id: Date.now(),
        text: "I'm sorry, I'm having trouble providing suggestions right now. Please try again later.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  const generateAIMealPlan = async () => {
    try {
      setLoading(true);

      const loadingMessage = {
        id: Date.now(),
        text: "Perfect! I'm generating a personalized meal plan based on your dietary preferences. This may take a moment...",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, loadingMessage]);

      const response = await aiAPI.generateAIMealPlan();

      if (response.success) {
        setGeneratedMealPlan(response);

        // Format the meal plan for display
        const mealPlanText = formatMealPlanForDisplay(response);

        const aiMessage = {
          id: Date.now() + 1,
          text: `${response.personalizedMessage}\n\n${mealPlanText}\n\n${response.nutritionalSummary}\n\nWhat would you like to do with this meal plan?`,
          isUser: false,
          timestamp: new Date(),
          showMealPlanActions: true,
        };
        setMessages(prev => [...prev, aiMessage]);
      }
    } catch (error) {
      console.error('Error generating meal plan:', error);
      const errorMessage = {
        id: Date.now() + 1,
        text: "I'm sorry, I had trouble generating your meal plan. Please try again later.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  const generateFamilyMealPlan = async () => {
    try {
      setLoading(true);

      const loadingMessage = {
        id: Date.now(),
        text: "Perfect! I'm generating a meal plan based on your family's dietary preferences and allergies. This may take a moment...",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, loadingMessage]);

      const response = await aiAPI.generateFamilyMealPlan();

      if (response.success) {
        setGeneratedMealPlan(response);

        // Format the meal plan for display
        const mealPlanText = formatMealPlanForDisplay(response);

        // Check if there are conflicts to notify user
        const conflictText = response.conflicts && response.conflicts.length > 0
          ? `\n\n⚠️ Dietary Conflicts Detected:\n${response.conflicts.join('\n')}\n\nI've created a meal plan that accommodates these conflicts as much as possible.`
          : '';

        const aiMessage = {
          id: Date.now() + 1,
          text: `${response.personalizedMessage}\n\n${mealPlanText}\n\n${response.nutritionalSummary}${conflictText}\n\nWhat would you like to do with this family meal plan?`,
          isUser: false,
          timestamp: new Date(),
          showMealPlanActions: true,
          isFamilyPlan: true,
        };
        setMessages(prev => [...prev, aiMessage]);
      }
    } catch (error) {
      console.error('Error generating family meal plan:', error);
      const errorMessage = {
        id: Date.now() + 1,
        text: "I'm sorry, I had trouble generating your family meal plan. Please try again later.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  const formatMealPlanForDisplay = (mealPlanData) => {
    const { mealPlan } = mealPlanData;
    let text = "🍽️ **Your Personalized Meal Plan:**\n\n";

    if (mealPlan.breakfast && mealPlan.breakfast.length > 0) {
      text += "🌅 **Breakfast:**\n";
      mealPlan.breakfast.forEach(meal => {
        text += `• ${meal.mealName}\n  ${meal.reason}\n\n`;
      });
    }

    if (mealPlan.lunch && mealPlan.lunch.length > 0) {
      text += "☀️ **Lunch:**\n";
      mealPlan.lunch.forEach(meal => {
        text += `• ${meal.mealName}\n  ${meal.reason}\n\n`;
      });
    }

    if (mealPlan.dinner && mealPlan.dinner.length > 0) {
      text += "🌙 **Dinner:**\n";
      mealPlan.dinner.forEach(meal => {
        text += `• ${meal.mealName}\n  ${meal.reason}\n\n`;
      });
    }

    return text;
  };

  const handleSaveMealPlan = async (date) => {
    try {
      setLoading(true);

      if (!generatedMealPlan) {
        throw new Error('No meal plan to save');
      }

      // Convert AI meal plan to the format expected by saveMealPlan API
      const mealPlanData = convertAIMealPlanToSaveFormat(generatedMealPlan, date);

      // Import the meal plan API
      const { default: axios } = await import('axios');
      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const token = localStorage.getItem('token');

      const response = await axios.post(`${API_BASE_URL}/meal-plans/save`, mealPlanData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        const successMessage = {
          id: Date.now(),
          text: `Great! Your meal plan has been saved to your calendar for ${new Date(date).toLocaleDateString()}. You can view it in your meal plan calendar.`,
          isUser: false,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, successMessage]);

        // Reset meal plan state
        setGeneratedMealPlan(null);
        setShowDatePicker(false);
      }
    } catch (error) {
      console.error('Error saving meal plan:', error);
      const errorMessage = {
        id: Date.now(),
        text: "I'm sorry, I had trouble saving your meal plan. Please try again.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  const convertAIMealPlanToSaveFormat = (aiMealPlan, date) => {
    const meals = [];
    const dateStr = new Date(date).toISOString().split('T')[0];

    // Convert breakfast meals
    if (aiMealPlan.mealPlan.breakfast) {
      aiMealPlan.mealPlan.breakfast.forEach(meal => {
        meals.push({
          date: dateStr,
          mealType: 'breakfast',
          mealData: {
            name: meal.mealName,
            instanceId: `${meal.mealName}-${Date.now()}-breakfast`,
            calories: 0, // Will be filled from database
            category: 'AI Generated',
            description: meal.reason
          }
        });
      });
    }

    // Convert lunch meals
    if (aiMealPlan.mealPlan.lunch) {
      aiMealPlan.mealPlan.lunch.forEach(meal => {
        meals.push({
          date: dateStr,
          mealType: 'lunch',
          mealData: {
            name: meal.mealName,
            instanceId: `${meal.mealName}-${Date.now()}-lunch`,
            calories: 0,
            category: 'AI Generated',
            description: meal.reason
          }
        });
      });
    }

    // Convert dinner meals
    if (aiMealPlan.mealPlan.dinner) {
      aiMealPlan.mealPlan.dinner.forEach(meal => {
        meals.push({
          date: dateStr,
          mealType: 'dinner',
          mealData: {
            name: meal.mealName,
            instanceId: `${meal.mealName}-${Date.now()}-dinner`,
            calories: 0,
            category: 'AI Generated',
            description: meal.reason
          }
        });
      });
    }

    return {
      name: `AI Generated Meal Plan - ${new Date(date).toLocaleDateString()}`,
      startDate: dateStr,
      endDate: dateStr,
      dietaryPreference: 'all',
      meals: meals,
      mealTimes: {
        breakfast: '08:00',
        lunch: '12:00',
        dinner: '18:00'
      }
    };
  };

  const sendMessage = async (e) => {
    e.preventDefault();
    if (!inputText.trim()) return;

    const userMessage = {
      id: Date.now(),
      text: inputText,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    const messageText = inputText;
    setInputText('');
    setLoading(true);

    try {
      // Check if user is responding about meal plan actions
      if (generatedMealPlan) {
        // Check for Save option
        if (messageText.toLowerCase().includes('save') || messageText.toLowerCase().includes('calendar')) {
          const aiMessage = {
            id: Date.now() + 1,
            text: "Perfect! Please select the date you'd like to schedule this meal plan:",
            isUser: false,
            timestamp: new Date(),
            showDatePicker: true,
          };
          setMessages(prev => [...prev, aiMessage]);
          setShowDatePicker(true);
          setLoading(false);
          return;
        }

        // Check for Edit option
        if (messageText.toLowerCase().includes('edit') || messageText.toLowerCase().includes('update') || messageText.toLowerCase().includes('change')) {
          setIsEditingMode(true);
          const aiMessage = {
            id: Date.now() + 1,
            text: "Great! I can help you edit your meal plan. Please tell me which dishes you'd like to change. For example, you can say 'Add Adobong kangkong as breakfast instead of Pinakbet' or 'Replace the lunch with Sinigang na baboy'.",
            isUser: false,
            timestamp: new Date(),
          };
          setMessages(prev => [...prev, aiMessage]);
          setLoading(false);
          return;
        }

        // Check for No thanks option
        if (messageText.toLowerCase().includes('no') || messageText.toLowerCase().includes('not') || messageText.toLowerCase().includes('thanks')) {
          const aiMessage = {
            id: Date.now() + 1,
            text: "No problem! Is there anything else I can help you with regarding your meal planning?",
            isUser: false,
            timestamp: new Date(),
          };
          setMessages(prev => [...prev, aiMessage]);
          setGeneratedMealPlan(null);
          setIsEditingMode(false);
          setLoading(false);
          return;
        }
      }

      // Check if user is in editing mode and wants to make specific changes
      if (isEditingMode && generatedMealPlan) {
        try {
          // Send the editing request to the dedicated edit endpoint
          const editResponse = await aiAPI.editMealPlan({
            currentMealPlan: generatedMealPlan.mealPlan,
            editRequest: messageText,
            isFamily: false
          });

          if (editResponse.success) {
            // Update the generated meal plan with the edited version
            const updatedMealPlan = {
              mealPlan: editResponse.mealPlan,
              nutritionalSummary: editResponse.nutritionalSummary,
              personalizedMessage: editResponse.personalizedMessage,
              conflicts: editResponse.conflicts || [],
              timestamp: editResponse.timestamp
            };

            setGeneratedMealPlan(updatedMealPlan);

            // Format the meal plan for display
            const formattedMealPlan = formatMealPlanForDisplay(updatedMealPlan);

            const updatedMessage = {
              id: Date.now() + 1,
              text: `${updatedMealPlan.personalizedMessage}\n\n${formattedMealPlan}\n\nWhat would you like to do with this updated meal plan?`,
              isUser: false,
              timestamp: new Date(),
              showMealPlanActions: true,
            };
            setMessages(prev => [...prev, updatedMessage]);
            setIsEditingMode(false);
            setLoading(false);
            return;
          } else {
            throw new Error(editResponse.message || 'Failed to edit meal plan');
          }
        } catch (error) {
          console.error('Error editing meal plan:', error);
          const errorMessage = {
            id: Date.now() + 1,
            text: "I'm sorry, I had trouble updating your meal plan. Please try rephrasing your request or try again.",
            isUser: false,
            timestamp: new Date(),
          };
          setMessages(prev => [...prev, errorMessage]);
          setLoading(false);
          return;
        }
      }

      const response = await aiAPI.sendChatMessage({
        message: messageText,
        includeProfile: true,
        includeMeals: true
      });

      if (response.success) {
        const aiMessage = {
          id: Date.now() + 1,
          text: response.response,
          isUser: false,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, aiMessage]);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage = {
        id: Date.now() + 1,
        text: "I'm sorry, I'm having trouble responding right now. Please try again later.",
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  const resetChat = () => {
    setMessages([]);
    setSelectedGoal(null);
    setSelectedHealthCondition(null);
    setSelectedBudget(null);
    setShowGoalSelection(true);
    setShowBudgetSelection(false);
    setInputText('');
    addWelcomeMessage();
  };

  return (
    <Layout>
      <div className="main-content">
        <div className="chat-container">
          <div className="chat-header">
            <h1>AI Meal Assistant</h1>
            <button onClick={resetChat} className="reset-chat-btn">
              🔄 Reset Chat
            </button>
          </div>

          <div className="chat-messages">
            {messages.map((message) => (
              <div key={message.id} className={`message ${message.isUser ? 'user-message' : 'ai-message'}`}>
                <div className="message-content">
                  <div className="message-text">
                    {message.text.split('\n').map((line, index) => (
                      <div key={index}>{line}</div>
                    ))}
                  </div>
                  <div className="message-timestamp">
                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </div>
                </div>

                {message.showHealthConditions && (
                  <div className="health-conditions-grid">
                    {healthConditions.map(condition => (
                      <button
                        key={condition.id}
                        className="health-condition-btn"
                        onClick={() => selectHealthCondition(condition)}
                      >
                        <div className="condition-name">{condition.name}</div>
                        <div className="condition-description">{condition.description}</div>
                      </button>
                    ))}
                  </div>
                )}

                {message.showMealPlanActions && generatedMealPlan && (
                  <div className="meal-plan-actions">
                    <button
                      className="action-btn primary"
                      onClick={() => {
                        const userMessage = {
                          id: Date.now(),
                          text: "Yes, please save this meal plan to my calendar",
                          isUser: true,
                          timestamp: new Date(),
                        };
                        setMessages(prev => [...prev, userMessage]);

                        const aiMessage = {
                          id: Date.now() + 1,
                          text: "Perfect! Please select the date you'd like to schedule this meal plan:",
                          isUser: false,
                          timestamp: new Date(),
                          showDatePicker: true,
                        };
                        setMessages(prev => [...prev, aiMessage]);
                        setShowDatePicker(true);
                      }}
                    >
                      Save to Calendar
                    </button>

                    <button
                      className="action-btn secondary"
                      onClick={() => {
                        const userMessage = {
                          id: Date.now(),
                          text: "No, I don't want to save this meal plan",
                          isUser: true,
                          timestamp: new Date(),
                        };
                        setMessages(prev => [...prev, userMessage]);

                        const aiMessage = {
                          id: Date.now() + 1,
                          text: "No problem! Is there anything else I can help you with regarding your meal planning?",
                          isUser: false,
                          timestamp: new Date(),
                        };
                        setMessages(prev => [...prev, aiMessage]);
                        setGeneratedMealPlan(null);
                      }}
                    >
                      No Thanks
                    </button>
                  </div>
                )}

                {message.showDatePicker && showDatePicker && (
                  <div className="date-picker-container">
                    <input
                      type="date"
                      value={selectedDate}
                      onChange={(e) => setSelectedDate(e.target.value)}
                      min={new Date().toISOString().split('T')[0]}
                      className="date-picker"
                    />
                    <button
                      className="action-btn primary"
                      onClick={() => {
                        if (selectedDate) {
                          handleSaveMealPlan(selectedDate);
                          setShowDatePicker(false);
                        }
                      }}
                      disabled={!selectedDate}
                    >
                      Confirm Date
                    </button>
                  </div>
                )}
              </div>
            ))}

            {/* Goal Selection */}
            {showGoalSelection && messages.length > 0 && (
              <div className="goals-selection">
                <h3>Select your goal:</h3>
                <div className="goals-grid">
                  {goals.map(goal => (
                    <button
                      key={goal.id}
                      className="goal-btn"
                      onClick={() => selectGoal(goal)}
                    >
                      <div className="goal-name">{goal.name}</div>
                      <div className="goal-description">{goal.description}</div>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Budget Range Selection */}
            {showBudgetSelection && (
              <div className="budget-selection">
                <h3>Select your budget range:</h3>
                <div className="budget-ranges-grid">
                  {budgetRanges.map(budget => (
                    <button
                      key={budget.id}
                      className="budget-range-btn"
                      onClick={() => selectBudgetRange(budget)}
                    >
                      <div className="budget-name">{budget.name}</div>
                      <div className="budget-description">{budget.description}</div>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {loading && (
              <div className="loading-message">
                <div className="typing-indicator">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
                <span>AI is thinking...</span>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Always show chat input */}
          <form onSubmit={sendMessage} className="chat-input-form">
            <div className="chat-input-container">
              <input
                type="text"
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                placeholder="Ask me about meals, nutrition, or dietary advice..."
                className="chat-input"
                disabled={loading}
                maxLength={500}
              />
              <button
                type="submit"
                disabled={!inputText.trim() || loading}
                className="send-btn"
              >
                Send
              </button>
            </div>
          </form>
        </div>
      </div>
    </Layout>
  );
};

export default Chat;
